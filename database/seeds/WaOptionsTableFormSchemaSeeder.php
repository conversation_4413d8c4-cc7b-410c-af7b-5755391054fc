<?php

declare(strict_types=1);

use Phinx\Seed\AbstractSeed;

class WaOptionsTableFormSchemaSeeder extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeders is available here:
     * https://book.cakephp.org/phinx/0/en/seeding.html
     */
    public function run(): void
    {
        // Channel user analytics schema
        $channelUserAnalyticsSchema = [
            'date' => [
                'field' => 'date',
                '_field_id' => '0',
                'comment' => '统计日期',
                'control' => 'datePicker',
                'control_args' => '',
                'list_show' => true,
                'enable_sort' => true,
                'searchable' => true,
                'search_type' => 'between',
                'form_show' => false,
            ],
            'channel_id' => [
                'field' => 'channel_id',
                '_field_id' => '1',
                'comment' => '渠道',
                'control' => 'select',
                'control_args' => 'url:/admin/channel/select?format=select',
                'list_show' => true,
                'enable_sort' => true,
                'searchable' => true,
                'search_type' => 'normal',
                'form_show' => false,
            ],
            'daily_active_users' => [
                'field' => 'daily_active_users',
                '_field_id' => '2',
                'comment' => '日活跃用户数',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'daily_new_users' => [
                'field' => 'daily_new_users',
                '_field_id' => '3',
                'comment' => '日新增用户数',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'total_sessions' => [
                'field' => 'total_sessions',
                '_field_id' => '4',
                'comment' => '总访问次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'total_session_duration_seconds' => [
                'field' => 'total_session_duration_seconds',
                '_field_id' => '5',
                'comment' => '总停留时长(秒)',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'avg_session_duration_seconds' => [
                'field' => 'avg_session_duration_seconds',
                '_field_id' => '6',
                'comment' => '平均停留时长(秒)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'avg_sessions_per_user' => [
                'field' => 'avg_sessions_per_user',
                '_field_id' => '7',
                'comment' => '人均访问次数',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'total_heartbeats' => [
                'field' => 'total_heartbeats',
                '_field_id' => '8',
                'comment' => '总心跳次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'avg_heartbeats_per_session' => [
                'field' => 'avg_heartbeats_per_session',
                '_field_id' => '9',
                'comment' => '平均每会话心跳次数',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_count' => [
                'field' => 'active_users_count',
                '_field_id' => '10',
                'comment' => '活跃用户数（排除新增用户）',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_sessions' => [
                'field' => 'active_users_sessions',
                '_field_id' => '11',
                'comment' => '活跃用户总访问次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_duration_seconds' => [
                'field' => 'active_users_duration_seconds',
                '_field_id' => '12',
                'comment' => '活跃用户总访问时长（秒）',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_avg_session_duration' => [
                'field' => 'active_users_avg_session_duration',
                '_field_id' => '13',
                'comment' => '活跃用户平均停留时长',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_avg_sessions_per_user' => [
                'field' => 'active_users_avg_sessions_per_user',
                '_field_id' => '14',
                'comment' => '活跃用户平均访问次数',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_sessions' => [
                'field' => 'new_users_sessions',
                '_field_id' => '15',
                'comment' => '新增用户总访问次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_duration_seconds' => [
                'field' => 'new_users_duration_seconds',
                '_field_id' => '16',
                'comment' => '新增用户总访问时长（秒）',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_avg_session_duration' => [
                'field' => 'new_users_avg_session_duration',
                '_field_id' => '17',
                'comment' => '新增用户平均停留时长',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_avg_sessions_per_user' => [
                'field' => 'new_users_avg_sessions_per_user',
                '_field_id' => '18',
                'comment' => '新增用户平均访问次数',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_1_retention_rate' => [
                'field' => 'day_1_retention_rate',
                '_field_id' => '19',
                'comment' => '次日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_3_retention_rate' => [
                'field' => 'day_3_retention_rate',
                '_field_id' => '20',
                'comment' => '3日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_7_retention_rate' => [
                'field' => 'day_7_retention_rate',
                '_field_id' => '21',
                'comment' => '7日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_30_retention_rate' => [
                'field' => 'day_30_retention_rate',
                '_field_id' => '22',
                'comment' => '30日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
        ];

        // Game user analytics schema
        $gameUserAnalyticsSchema = [
            'date' => [
                'field' => 'date',
                '_field_id' => '0',
                'comment' => '统计日期',
                'control' => 'datePicker',
                'control_args' => '',
                'list_show' => true,
                'enable_sort' => true,
                'searchable' => true,
                'search_type' => 'between',
                'form_show' => false,
            ],
            'channel_id' => [
                'field' => 'channel_id',
                '_field_id' => '1',
                'comment' => '渠道',
                'control' => 'select',
                'control_args' => 'url:/admin/channel/select?format=select',
                'list_show' => true,
                'enable_sort' => true,
                'searchable' => true,
                'search_type' => 'normal',
                'form_show' => false,
            ],
            'game_id' => [
                'field' => 'game_id',
                '_field_id' => '2',
                'comment' => '游戏',
                'control' => 'select',
                'control_args' => 'url:/admin/game/select?format=select',
                'list_show' => true,
                'enable_sort' => true,
                'searchable' => true,
                'search_type' => 'normal',
                'form_show' => false,
            ],
            'daily_active_users' => [
                'field' => 'daily_active_users',
                '_field_id' => '3',
                'comment' => '日活跃用户数',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'daily_new_users' => [
                'field' => 'daily_new_users',
                '_field_id' => '4',
                'comment' => '日新增用户数',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'total_sessions' => [
                'field' => 'total_sessions',
                '_field_id' => '5',
                'comment' => '总访问次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'total_session_duration_seconds' => [
                'field' => 'total_session_duration_seconds',
                '_field_id' => '6',
                'comment' => '总停留时长(秒)',
                'control' => 'inputNumber',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'avg_session_duration_seconds' => [
                'field' => 'avg_session_duration_seconds',
                '_field_id' => '7',
                'comment' => '平均停留时长(秒)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'avg_sessions_per_user' => [
                'field' => 'avg_sessions_per_user',
                '_field_id' => '8',
                'comment' => '人均访问次数',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'total_heartbeats' => [
                'field' => 'total_heartbeats',
                '_field_id' => '9',
                'comment' => '总心跳次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'avg_heartbeats_per_session' => [
                'field' => 'avg_heartbeats_per_session',
                '_field_id' => '10',
                'comment' => '平均每会话心跳次数',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_count' => [
                'field' => 'active_users_count',
                '_field_id' => '11',
                'comment' => '活跃用户数（排除新增用户）',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_sessions' => [
                'field' => 'active_users_sessions',
                '_field_id' => '12',
                'comment' => '活跃用户总访问次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_duration_seconds' => [
                'field' => 'active_users_duration_seconds',
                '_field_id' => '13',
                'comment' => '活跃用户总访问时长（秒）',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_avg_session_duration' => [
                'field' => 'active_users_avg_session_duration',
                '_field_id' => '14',
                'comment' => '活跃用户平均停留时长',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_avg_sessions_per_user' => [
                'field' => 'active_users_avg_sessions_per_user',
                '_field_id' => '15',
                'comment' => '活跃用户平均访问次数',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_sessions' => [
                'field' => 'new_users_sessions',
                '_field_id' => '16',
                'comment' => '新增用户总访问次数',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_duration_seconds' => [
                'field' => 'new_users_duration_seconds',
                '_field_id' => '17',
                'comment' => '新增用户总访问时长（秒）',
                'control' => 'inputNumber',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_avg_session_duration' => [
                'field' => 'new_users_avg_session_duration',
                '_field_id' => '18',
                'comment' => '新增用户平均停留时长',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_avg_sessions_per_user' => [
                'field' => 'new_users_avg_sessions_per_user',
                '_field_id' => '19',
                'comment' => '新增用户平均访问次数',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_1_retention_rate' => [
                'field' => 'day_1_retention_rate',
                '_field_id' => '20',
                'comment' => '次日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_3_retention_rate' => [
                'field' => 'day_3_retention_rate',
                '_field_id' => '21',
                'comment' => '3日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_7_retention_rate' => [
                'field' => 'day_7_retention_rate',
                '_field_id' => '22',
                'comment' => '7日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'day_30_retention_rate' => [
                'field' => 'day_30_retention_rate',
                '_field_id' => '23',
                'comment' => '30日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'list_show' => true,
                'search_type' => 'normal',
                'form_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_day_1_retention_rate' => [
                'field' => 'active_users_day_1_retention_rate',
                '_field_id' => '24',
                'comment' => '活跃用户次日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_day_3_retention_rate' => [
                'field' => 'active_users_day_3_retention_rate',
                '_field_id' => '25',
                'comment' => '活跃用户3日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_day_7_retention_rate' => [
                'field' => 'active_users_day_7_retention_rate',
                '_field_id' => '26',
                'comment' => '活跃用户7日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'active_users_day_30_retention_rate' => [
                'field' => 'active_users_day_30_retention_rate',
                '_field_id' => '27',
                'comment' => '活跃用户30日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_day_1_retention_rate' => [
                'field' => 'new_users_day_1_retention_rate',
                '_field_id' => '28',
                'comment' => '新增用户次日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_day_3_retention_rate' => [
                'field' => 'new_users_day_3_retention_rate',
                '_field_id' => '29',
                'comment' => '新增用户3日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_day_7_retention_rate' => [
                'field' => 'new_users_day_7_retention_rate',
                '_field_id' => '30',
                'comment' => '新增用户7日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'new_users_day_30_retention_rate' => [
                'field' => 'new_users_day_30_retention_rate',
                '_field_id' => '31',
                'comment' => '新增用户30日留存率(0-1)',
                'control' => 'input',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'created_at' => [
                'field' => 'created_at',
                '_field_id' => '32',
                'comment' => '创建时间',
                'control' => 'dateTimePicker',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
            'updated_at' => [
                'field' => 'updated_at',
                '_field_id' => '33',
                'comment' => '更新时间',
                'control' => 'dateTimePicker',
                'control_args' => '',
                'search_type' => 'normal',
                'form_show' => false,
                'list_show' => false,
                'enable_sort' => false,
                'searchable' => false,
            ],
        ];

        // Prepare data for insertion
        $data = [
            [
                'name' => 'table_form_schema_fact_daily_channel_user_analytics',
                'value' => \json_encode($channelUserAnalyticsSchema, \JSON_UNESCAPED_SLASHES),
                'created_at' => '2022-08-15 00:00:00',
                'updated_at' => '2025-06-25 16:49:10',
            ],
            [
                'name' => 'table_form_schema_fact_daily_game_user_analytics',
                'value' => \json_encode($gameUserAnalyticsSchema, \JSON_UNESCAPED_SLASHES),
                'created_at' => '2022-08-15 00:00:00',
                'updated_at' => '2025-06-25 16:49:10',
            ],
        ];

        // Insert data into wa_options table
        $table = $this->table('wa_options');
        $table->insert($data)
              ->saveData();
    }
}
