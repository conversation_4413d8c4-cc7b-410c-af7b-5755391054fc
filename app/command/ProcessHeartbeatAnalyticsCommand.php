<?php

declare(strict_types=1);

namespace app\command;

use app\model\DailyChannelUserAnalytics;
use app\model\DailyGameUserAnalytics;
use app\model\HeartbeatSession;
use app\model\UserFirstActivity;
use app\model\UserRetentionTracking;
use plugin\user\app\model\User;
use support\Db;
use support\Log;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * 心跳数据分析处理命令.
 *
 * 处理心跳会话数据并生成用户分析统计
 */
class ProcessHeartbeatAnalyticsCommand extends Command
{
    /**
     * 命令名称.
     */
    protected static $defaultName = 'analytics:process-heartbeat';
    /**
     * 配置命令.
     */
    protected function configure(): void
    {
        $this->setDescription('Process heartbeat session data into analytics tables')
            ->addArgument(
                'date',
                InputArgument::OPTIONAL,
                'Date to process (YYYY-MM-DD format). Defaults to yesterday if not provided.',
                null
            )
            ->addOption(
                'dry-run',
                null,
                InputOption::VALUE_NONE,
                'Show what would be processed without actually doing it'
            )
            ->addOption(
                'force',
                'f',
                InputOption::VALUE_NONE,
                'Force reprocessing even if data already exists'
            )
            ->addOption(
                'channel',
                'c',
                InputOption::VALUE_OPTIONAL,
                'Process only specific channel ID',
                null
            )
            ->addOption(
                'game',
                'g',
                InputOption::VALUE_OPTIONAL,
                'Process only specific game ID',
                null
            );
    }

    /**
     * 执行命令.
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $date = $input->getArgument('date') ?: date('Y-m-d', strtotime('-1 day'));
        $isDryRun = $input->getOption('dry-run');
        $isForce = $input->getOption('force');
        $channelFilter = $input->getOption('channel') ? (int) $input->getOption('channel') : null;
        $gameFilter = $input->getOption('game') ? (int) $input->getOption('game') : null;

        // 验证日期格式
        if (!$this->isValidDate($date)) {
            $output->writeln('<error>Invalid date format. Please use YYYY-MM-DD format.</error>');
            return Command::FAILURE;
        }

        // 验证日期不能是未来日期
        if (strtotime($date) > strtotime('today')) {
            $output->writeln('<error>Cannot process future dates. Please provide a date that is today or earlier.</error>');
            return Command::FAILURE;
        }

        // 验证渠道和游戏ID
        if ($channelFilter !== null && $channelFilter <= 0) {
            $output->writeln('<error>Channel ID must be a positive integer.</error>');
            return Command::FAILURE;
        }

        if ($gameFilter !== null && $gameFilter <= 0) {
            $output->writeln('<error>Game ID must be a positive integer.</error>');
            return Command::FAILURE;
        }

        $output->writeln("<info>Processing heartbeat analytics for date: {$date}</info>");
        
        if ($isDryRun) {
            $output->writeln('<comment>DRY RUN MODE - No changes will be made</comment>');
        }

        if ($channelFilter) {
            $output->writeln("<info>Filtering by channel ID: {$channelFilter}</info>");
        }

        if ($gameFilter) {
            $output->writeln("<info>Filtering by game ID: {$gameFilter}</info>");
        }

        try {
            // 开始数据库事务
            if (!$isDryRun) {
                Db::beginTransaction();
            }

            // 获取需要处理的心跳会话数据
            $sessions = $this->getHeartbeatSessionsForDate($date, $channelFilter, $gameFilter);
            
            if ($sessions->isEmpty()) {
                $output->writeln('<comment>No heartbeat sessions found for the specified date and filters.</comment>');
                return Command::SUCCESS;
            }

            $output->writeln("<info>Found {$sessions->count()} heartbeat sessions to process</info>");

            // 检查是否已存在数据
            if (!$isForce && !$isDryRun && $this->hasExistingData($date, $channelFilter, $gameFilter)) {
                $output->writeln('<comment>Analytics data already exists for this date. Use --force to reprocess.</comment>');
                return Command::SUCCESS;
            }

            // 处理用户首次活动记录
            $this->processUserFirstActivity($sessions, $date, $output, $isDryRun);

            // 处理用户留存跟踪数据
            $this->processUserRetentionTracking($sessions, $date, $output, $isDryRun);

            // 处理渠道级别分析数据
            $this->processChannelAnalytics($sessions, $date, $output, $isDryRun);

            // 处理游戏级别分析数据
            $this->processGameAnalytics($sessions, $date, $output, $isDryRun);

            // 提交事务
            if (!$isDryRun) {
                Db::commit();
            }

            $output->writeln('<info>Analytics processing completed successfully!</info>');

            Log::info('Heartbeat analytics processing completed', [
                'date' => $date,
                'sessions_processed' => $sessions->count(),
                'dry_run' => $isDryRun,
                'channel_filter' => $channelFilter,
                'game_filter' => $gameFilter,
            ]);

            return Command::SUCCESS;

        } catch (\Throwable $e) {
            // 回滚事务
            if (!$isDryRun) {
                Db::rollBack();
            }

            $output->writeln("<error>Error during analytics processing: {$e->getMessage()}</error>");
            
            Log::error('Heartbeat analytics processing failed', [
                'date' => $date,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * 验证日期格式.
     */
    private function isValidDate(string $date): bool
    {
        $dateTime = \DateTime::createFromFormat('Y-m-d', $date);
        return $dateTime && $dateTime->format('Y-m-d') === $date;
    }

    /**
     * 获取指定日期的心跳会话数据.
     */
    private function getHeartbeatSessionsForDate(string $date, ?int $channelFilter = null, ?int $gameFilter = null): \Illuminate\Database\Eloquent\Collection
    {
        $startTimestamp = strtotime($date . ' 00:00:00') * 1000; // 转换为毫秒
        $endTimestamp = strtotime($date . ' 23:59:59') * 1000; // 转换为毫秒

        $query = HeartbeatSession::whereBetween('session_start_time', [$startTimestamp, $endTimestamp]);

        if ($channelFilter !== null) {
            $query->where('channel_id', $channelFilter);
        }

        if ($gameFilter !== null) {
            $query->where('game_id', $gameFilter);
        }

        return $query->orderBy('session_start_time')->get();
    }

    /**
     * 检查是否已存在分析数据.
     */
    private function hasExistingData(string $date, ?int $channelFilter = null, ?int $gameFilter = null): bool
    {
        // 检查渠道级别数据
        $channelQuery = DailyChannelUserAnalytics::where('date', $date);
        if ($channelFilter !== null) {
            $channelQuery->where('channel_id', $channelFilter);
        }
        
        if ($channelQuery->exists()) {
            return true;
        }

        // 检查游戏级别数据
        $gameQuery = DailyGameUserAnalytics::where('date', $date);
        if ($channelFilter !== null) {
            $gameQuery->where('channel_id', $channelFilter);
        }
        if ($gameFilter !== null) {
            $gameQuery->where('game_id', $gameFilter);
        }

        return $gameQuery->exists();
    }

    /**
     * 处理用户首次活动记录.
     */
    private function processUserFirstActivity(\Illuminate\Database\Eloquent\Collection $sessions, string $date, OutputInterface $output, bool $isDryRun): void
    {
        $output->writeln('<info>Processing user first activity records...</info>');

        $processedUsers = [];
        $newUsersCount = 0;

        foreach ($sessions as $session) {
            $userKey = "{$session->user_id}_{$session->channel_id}_{$session->game_id}";

            // 避免重复处理同一用户
            if (isset($processedUsers[$userKey])) {
                continue;
            }
            $processedUsers[$userKey] = true;

            // 检查是否已存在首次活动记录
            $existingRecord = UserFirstActivity::findByUserChannelAndGame(
                $session->user_id,
                $session->channel_id,
                $session->game_id
            );

            if ($existingRecord) {
                continue; // 已存在记录，跳过
            }

            // 获取用户注册信息
            try {
                $user = User::find($session->user_id);
                if ($user && $user->join_time) {
                    $registrationDate = $this->formatDateToString($user->join_time);
                } else {
                    $registrationDate = null;
                }
            } catch (\Throwable $e) {
                Log::warning('Failed to fetch user registration info', [
                    'user_id' => $session->user_id,
                    'error' => $e->getMessage(),
                ]);
                $registrationDate = null;
            }

            // 判断是否为新用户（首次活动日期等于注册日期，或注册日期为空时认为是新用户）
            $isNewUser = !$registrationDate || $registrationDate === $date;

            if ($isNewUser) {
                $newUsersCount++;
            }

            if (!$isDryRun) {
                UserFirstActivity::updateOrCreateByUserChannelAndGame(
                    $session->user_id,
                    $session->channel_id,
                    $session->game_id,
                    [
                        'first_activity_date' => $date,
                        'first_session_id' => $session->session_id,
                        'first_session_start_time' => $session->session_start_time,
                        'registration_date' => $registrationDate,
                        'is_new_user' => $isNewUser,
                    ]
                );
            }
        }

        $output->writeln("<info>Processed {$newUsersCount} new users for first activity tracking</info>");
    }

    /**
     * 处理用户留存跟踪数据.
     */
    private function processUserRetentionTracking(\Illuminate\Database\Eloquent\Collection $sessions, string $date, OutputInterface $output, bool $isDryRun): void
    {
        $output->writeln('<info>Processing user retention tracking data...</info>');

        // 按用户分组会话数据
        $userSessions = $sessions->groupBy(function ($session) {
            return "{$session->user_id}_{$session->channel_id}_{$session->game_id}";
        });

        $processedCount = 0;

        foreach ($userSessions as $userSessionGroup) {
            $firstSession = $userSessionGroup->first();

            // 获取用户首次活动记录
            $firstActivity = UserFirstActivity::findByUserChannelAndGame(
                $firstSession->user_id,
                $firstSession->channel_id,
                $firstSession->game_id
            );

            if (!$firstActivity) {
                continue; // 没有首次活动记录，跳过
            }

            // 计算距离首次活动的天数
            $firstActivityDate = $this->formatDateToString($firstActivity->first_activity_date);
            $daysSinceFirst = (strtotime($date) - strtotime($firstActivityDate)) / 86400;

            // 计算当日会话统计
            $sessionCount = $userSessionGroup->count();
            $totalDuration = $userSessionGroup->sum('duration_seconds');
            $totalHeartbeats = $userSessionGroup->sum('heartbeat_count');

            if (!$isDryRun) {
                UserRetentionTracking::updateOrCreateByUserChannelGameAndDate(
                    $firstSession->user_id,
                    $firstSession->channel_id,
                    $firstSession->game_id,
                    $date,
                    [
                        'cohort_date' => $firstActivityDate,
                        'days_since_first_activity' => (int) $daysSinceFirst,
                        'session_count' => $sessionCount,
                        'total_session_duration_seconds' => $totalDuration,
                        'heartbeat_count' => $totalHeartbeats,
                    ]
                );
            }

            $processedCount++;
        }

        $output->writeln("<info>Processed retention tracking for {$processedCount} users</info>");
    }

    /**
     * 处理渠道级别分析数据.
     */
    private function processChannelAnalytics(\Illuminate\Database\Eloquent\Collection $sessions, string $date, OutputInterface $output, bool $isDryRun): void
    {
        $output->writeln('<info>Processing channel-level analytics...</info>');

        // 按渠道分组会话数据
        $channelSessions = $sessions->groupBy('channel_id');

        foreach ($channelSessions as $channelId => $channelSessionGroup) {
            $channelIdInt = (int) $channelId;

            // 获取当日新增用户ID列表
            $newUserIds = $this->getNewUserIds($date, $channelIdInt);

            // 分离新增用户和活跃用户的会话数据
            $newUserSessions = $channelSessionGroup->filter(function ($session) use ($newUserIds) {
                return in_array($session->user_id, $newUserIds);
            });

            $activeUserSessions = $channelSessionGroup->filter(function ($session) use ($newUserIds) {
                return !in_array($session->user_id, $newUserIds);
            });

            // 计算活跃用户指标
            $activeUserMetrics = $this->calculateUserMetrics($activeUserSessions);

            // 计算新增用户指标
            $newUserMetrics = $this->calculateUserMetrics($newUserSessions);

            // 计算总体指标（保持向后兼容）
            $totalMetrics = $this->calculateUserMetrics($channelSessionGroup);

            // 计算新用户数（从数据库获取准确数据）
            $dailyNewUsers = UserFirstActivity::getNewUserCountByDate($date, $channelIdInt);

            // 计算留存率
            $retentionRates = $this->calculateRetentionRates($date, $channelIdInt);
            $newUserRetentionRates = $this->calculateNewUserRetentionRates($date, $channelIdInt);
            $activeUserRetentionRates = $this->calculateActiveUserRetentionRates($date, $channelIdInt);

            $analyticsData = [
                // 总体指标（保持向后兼容）
                'daily_active_users' => $totalMetrics['user_count'],
                'daily_new_users' => $dailyNewUsers,
                'total_sessions' => $totalMetrics['sessions'],
                'total_session_duration_seconds' => $totalMetrics['duration'],
                'avg_session_duration_seconds' => $totalMetrics['avg_session_duration'],
                'avg_sessions_per_user' => $totalMetrics['avg_sessions_per_user'],
                'total_heartbeats' => $totalMetrics['heartbeats'],
                'avg_heartbeats_per_session' => $totalMetrics['avg_heartbeats_per_session'],

                // 活跃用户指标（排除新增用户）
                'active_users_count' => $activeUserMetrics['user_count'],
                'active_users_sessions' => $activeUserMetrics['sessions'],
                'active_users_duration_seconds' => $activeUserMetrics['duration'],
                'active_users_avg_session_duration' => $activeUserMetrics['avg_session_duration'],
                'active_users_avg_sessions_per_user' => $activeUserMetrics['avg_sessions_per_user'],

                // 新增用户指标
                'new_users_sessions' => $newUserMetrics['sessions'],
                'new_users_duration_seconds' => $newUserMetrics['duration'],
                'new_users_avg_session_duration' => $newUserMetrics['avg_session_duration'],
                'new_users_avg_sessions_per_user' => $newUserMetrics['avg_sessions_per_user'],

                // 总体留存率（保持向后兼容）
                'day_1_retention_rate' => $retentionRates['day_1'] ?? null,
                'day_7_retention_rate' => $retentionRates['day_7'] ?? null,
                'day_30_retention_rate' => $retentionRates['day_30'] ?? null,

                // 新增用户留存率
                'new_users_day_1_retention_rate' => $newUserRetentionRates['day_1'],
                'new_users_day_7_retention_rate' => $newUserRetentionRates['day_7'],
                'new_users_day_30_retention_rate' => $newUserRetentionRates['day_30'],

                // 活跃用户留存率
                'active_users_day_1_retention_rate' => $activeUserRetentionRates['day_1'],
                'active_users_day_7_retention_rate' => $activeUserRetentionRates['day_7'],
                'active_users_day_30_retention_rate' => $activeUserRetentionRates['day_30'],
            ];

            // 验证数据一致性
            $this->validateAnalyticsData($analyticsData);

            if (!$isDryRun) {
                try {
                    DailyChannelUserAnalytics::updateOrCreateByDateAndChannel($date, $channelIdInt, $analyticsData);
                } catch (\Throwable $e) {
                    Log::error('Failed to save channel analytics', [
                        'date' => $date,
                        'channel_id' => $channelId,
                        'error' => $e->getMessage(),
                    ]);
                    throw $e;
                }
            }

            $output->writeln("<info>Channel {$channelId}: Total Users={$totalMetrics['user_count']} (Active={$activeUserMetrics['user_count']}, New={$dailyNewUsers}), Sessions={$totalMetrics['sessions']}</info>");
        }
    }

    /**
     * 处理游戏级别分析数据.
     */
    private function processGameAnalytics(\Illuminate\Database\Eloquent\Collection $sessions, string $date, OutputInterface $output, bool $isDryRun): void
    {
        $output->writeln('<info>Processing game-level analytics...</info>');

        // 按渠道和游戏分组会话数据
        $gameChannelSessions = $sessions->groupBy(function ($session) {
            return "{$session->channel_id}_{$session->game_id}";
        });

        foreach ($gameChannelSessions as $key => $gameSessionGroup) {
            [$channelId, $gameId] = explode('_', $key);
            $channelId = (int) $channelId;
            $gameId = (int) $gameId;

            // 获取当日新增用户ID列表
            $newUserIds = $this->getNewUserIds($date, $channelId, $gameId);

            // 分离新增用户和活跃用户的会话数据
            $newUserSessions = $gameSessionGroup->filter(function ($session) use ($newUserIds) {
                return in_array($session->user_id, $newUserIds);
            });

            $activeUserSessions = $gameSessionGroup->filter(function ($session) use ($newUserIds) {
                return !in_array($session->user_id, $newUserIds);
            });

            // 计算活跃用户指标
            $activeUserMetrics = $this->calculateUserMetrics($activeUserSessions);

            // 计算新增用户指标
            $newUserMetrics = $this->calculateUserMetrics($newUserSessions);

            // 计算总体指标（保持向后兼容）
            $totalMetrics = $this->calculateUserMetrics($gameSessionGroup);

            // 计算新用户数（从数据库获取准确数据）
            $dailyNewUsers = UserFirstActivity::getNewUserCountByDate($date, $channelId, $gameId);

            // 计算留存率
            $retentionRates = $this->calculateRetentionRates($date, $channelId, $gameId);
            $newUserRetentionRates = $this->calculateNewUserRetentionRates($date, $channelId, $gameId);
            $activeUserRetentionRates = $this->calculateActiveUserRetentionRates($date, $channelId, $gameId);

            $analyticsData = [
                // 总体指标（保持向后兼容）
                'daily_active_users' => $totalMetrics['user_count'],
                'daily_new_users' => $dailyNewUsers,
                'total_sessions' => $totalMetrics['sessions'],
                'total_session_duration_seconds' => $totalMetrics['duration'],
                'avg_session_duration_seconds' => $totalMetrics['avg_session_duration'],
                'avg_sessions_per_user' => $totalMetrics['avg_sessions_per_user'],
                'total_heartbeats' => $totalMetrics['heartbeats'],
                'avg_heartbeats_per_session' => $totalMetrics['avg_heartbeats_per_session'],

                // 活跃用户指标（排除新增用户）
                'active_users_count' => $activeUserMetrics['user_count'],
                'active_users_sessions' => $activeUserMetrics['sessions'],
                'active_users_duration_seconds' => $activeUserMetrics['duration'],
                'active_users_avg_session_duration' => $activeUserMetrics['avg_session_duration'],
                'active_users_avg_sessions_per_user' => $activeUserMetrics['avg_sessions_per_user'],

                // 新增用户指标
                'new_users_sessions' => $newUserMetrics['sessions'],
                'new_users_duration_seconds' => $newUserMetrics['duration'],
                'new_users_avg_session_duration' => $newUserMetrics['avg_session_duration'],
                'new_users_avg_sessions_per_user' => $newUserMetrics['avg_sessions_per_user'],

                // 总体留存率（保持向后兼容）
                'day_1_retention_rate' => $retentionRates['day_1'] ?? null,
                'day_7_retention_rate' => $retentionRates['day_7'] ?? null,
                'day_30_retention_rate' => $retentionRates['day_30'] ?? null,

                // 新增用户留存率
                'new_users_day_1_retention_rate' => $newUserRetentionRates['day_1'],
                'new_users_day_7_retention_rate' => $newUserRetentionRates['day_7'],
                'new_users_day_30_retention_rate' => $newUserRetentionRates['day_30'],

                // 活跃用户留存率
                'active_users_day_1_retention_rate' => $activeUserRetentionRates['day_1'],
                'active_users_day_7_retention_rate' => $activeUserRetentionRates['day_7'],
                'active_users_day_30_retention_rate' => $activeUserRetentionRates['day_30'],
            ];

            // 验证数据一致性
            $this->validateAnalyticsData($analyticsData);

            if (!$isDryRun) {
                try {
                    DailyGameUserAnalytics::updateOrCreateByDateChannelAndGame($date, $channelId, $gameId, $analyticsData);
                } catch (\Throwable $e) {
                    Log::error('Failed to save game analytics', [
                        'date' => $date,
                        'channel_id' => $channelId,
                        'game_id' => $gameId,
                        'error' => $e->getMessage(),
                    ]);
                    throw $e;
                }
            }

            $output->writeln("<info>Channel {$channelId}, Game {$gameId}: Total Users={$totalMetrics['user_count']} (Active={$activeUserMetrics['user_count']}, New={$dailyNewUsers}), Sessions={$totalMetrics['sessions']}</info>");
        }
    }

    /**
     * 计算留存率.
     */
    private function calculateRetentionRates(string $date, int $channelId, ?int $gameId = null): array
    {
        $retentionRates = [];

        // 计算1日、7日、30日留存率
        $retentionDays = [1, 7, 30];

        foreach ($retentionDays as $days) {
            $cohortDate = date('Y-m-d', strtotime($date . " -{$days} days"));

            // 只有当群组日期存在足够的历史数据时才计算留存率
            if (strtotime($cohortDate) >= strtotime('-30 days')) {
                $retentionRate = UserRetentionTracking::calculateRetentionRate($cohortDate, $days, $channelId, $gameId);
                $retentionRates["day_{$days}"] = $retentionRate > 0 ? $retentionRate : null;
            } else {
                $retentionRates["day_{$days}"] = null;
            }
        }

        return $retentionRates;
    }

    /**
     * 将日期对象转换为字符串格式.
     */
    private function formatDateToString($date): string
    {
        if ($date instanceof \Carbon\Carbon) {
            return $date->format('Y-m-d');
        }

        if ($date instanceof \DateTime) {
            return $date->format('Y-m-d');
        }

        // Handle string dates (including datetime strings)
        if (is_string($date)) {
            $timestamp = strtotime($date);
            if ($timestamp !== false) {
                return date('Y-m-d', $timestamp);
            }
        }

        return (string) $date;
    }

    /**
     * 获取指定日期的新增用户ID列表.
     */
    private function getNewUserIds(string $date, int $channelId, ?int $gameId = null): array
    {
        $query = UserFirstActivity::where('first_activity_date', $date)
                                 ->where('channel_id', $channelId)
                                 ->where('is_new_user', true);

        if ($gameId !== null) {
            $query->where('game_id', $gameId);
        }

        return $query->pluck('user_id')->toArray();
    }

    /**
     * 计算用户指标.
     */
    private function calculateUserMetrics(\Illuminate\Database\Eloquent\Collection $sessions): array
    {
        if ($sessions->isEmpty()) {
            return [
                'user_count' => 0,
                'sessions' => 0,
                'duration' => 0,
                'heartbeats' => 0,
                'avg_session_duration' => 0,
                'avg_sessions_per_user' => 0,
                'avg_heartbeats_per_session' => 0,
            ];
        }

        $userCount = $sessions->unique('user_id')->count();
        $totalSessions = $sessions->count();
        $totalDuration = $sessions->sum('duration_seconds');
        $totalHeartbeats = $sessions->sum('heartbeat_count');

        return [
            'user_count' => $userCount,
            'sessions' => $totalSessions,
            'duration' => $totalDuration,
            'heartbeats' => $totalHeartbeats,
            'avg_session_duration' => $totalSessions > 0 ? round($totalDuration / $totalSessions, 2) : 0,
            'avg_sessions_per_user' => $userCount > 0 ? round($totalSessions / $userCount, 2) : 0,
            'avg_heartbeats_per_session' => $totalSessions > 0 ? round($totalHeartbeats / $totalSessions, 2) : 0,
        ];
    }

    /**
     * 获取当日活跃的老用户ID列表（排除新增用户）
     */
    private function getActiveUserIds(string $date, int $channelId, ?int $gameId = null, array $excludeUserIds = []): array
    {
        $query = HeartbeatSession::whereDate(DB::raw('FROM_UNIXTIME(session_start_time/1000)'), $date)
                                ->where('channel_id', $channelId);

        if ($gameId !== null) {
            $query->where('game_id', $gameId);
        }

        if (!empty($excludeUserIds)) {
            $query->whereNotIn('user_id', $excludeUserIds);
        }

        return $query->distinct('user_id')->pluck('user_id')->toArray();
    }

    /**
     * 获取在目标日期有活动的用户数量
     */
    private function getRetainedUserCount(array $userIds, string $targetDate, int $channelId, ?int $gameId = null): int
    {
        if (empty($userIds)) {
            return 0;
        }

        $query = HeartbeatSession::whereDate(DB::raw('FROM_UNIXTIME(session_start_time/1000)'), $targetDate)
                                ->where('channel_id', $channelId)
                                ->whereIn('user_id', $userIds);

        if ($gameId !== null) {
            $query->where('game_id', $gameId);
        }

        return $query->distinct('user_id')->count();
    }

    /**
     * 计算新增用户留存率
     */
    private function calculateNewUserRetentionRates(string $cohortDate, int $channelId, ?int $gameId = null): array
    {
        // 获取基准群体：当日新增用户
        $newUserIds = $this->getNewUserIds($cohortDate, $channelId, $gameId);

        if (empty($newUserIds)) {
            return ['day_1' => null, 'day_7' => null, 'day_30' => null];
        }

        $cohortSize = count($newUserIds);
        $retentionRates = [];

        foreach ([1, 7, 30] as $days) {
            $targetDate = date('Y-m-d', strtotime($cohortDate . " +{$days} days"));

            // 计算在目标日期有活动的新增用户数
            $retainedCount = $this->getRetainedUserCount($newUserIds, $targetDate, $channelId, $gameId);

            $retentionRates["day_{$days}"] = $retainedCount > 0 ? round($retainedCount / $cohortSize, 4) : null;
        }

        return $retentionRates;
    }

    /**
     * 计算活跃用户留存率
     */
    private function calculateActiveUserRetentionRates(string $cohortDate, int $channelId, ?int $gameId = null): array
    {
        // 获取基准群体：当日活跃的老用户（排除新增用户）
        $newUserIds = $this->getNewUserIds($cohortDate, $channelId, $gameId);
        $activeUserIds = $this->getActiveUserIds($cohortDate, $channelId, $gameId, $newUserIds);

        if (empty($activeUserIds)) {
            return ['day_1' => null, 'day_7' => null, 'day_30' => null];
        }

        $cohortSize = count($activeUserIds);
        $retentionRates = [];

        foreach ([1, 7, 30] as $days) {
            $targetDate = date('Y-m-d', strtotime($cohortDate . " +{$days} days"));

            // 计算在目标日期有活动的活跃用户数
            $retainedCount = $this->getRetainedUserCount($activeUserIds, $targetDate, $channelId, $gameId);

            $retentionRates["day_{$days}"] = $retainedCount > 0 ? round($retainedCount / $cohortSize, 4) : null;
        }

        return $retentionRates;
    }

    /**
     * 验证留存率递减趋势
     */
    private function validateRetentionTrend(array $analyticsData, string $userType): void
    {
        $prefix = $userType === 'new_users' ? 'new_users_' : 'active_users_';

        $day1 = $analyticsData[$prefix . 'day_1_retention_rate'] ?? null;
        $day7 = $analyticsData[$prefix . 'day_7_retention_rate'] ?? null;
        $day30 = $analyticsData[$prefix . 'day_30_retention_rate'] ?? null;

        // 只在有数据时验证趋势
        if ($day1 !== null && $day7 !== null && $day1 < $day7) {
            echo "Warning: {$userType} 1-day retention ({$day1}) is less than 7-day retention ({$day7})\n";
        }

        if ($day7 !== null && $day30 !== null && $day7 < $day30) {
            echo "Warning: {$userType} 7-day retention ({$day7}) is less than 30-day retention ({$day30})\n";
        }
    }

    /**
     * 验证分析数据的一致性.
     */
    private function validateAnalyticsData(array $analyticsData): void
    {
        // 验证总数等于分项之和
        $expectedTotalUsers = $analyticsData['active_users_count'] + $analyticsData['daily_new_users'];
        $expectedTotalSessions = $analyticsData['active_users_sessions'] + $analyticsData['new_users_sessions'];
        $expectedTotalDuration = $analyticsData['active_users_duration_seconds'] + $analyticsData['new_users_duration_seconds'];

        if ($expectedTotalUsers !== $analyticsData['daily_active_users']) {
            throw new \RuntimeException("User count validation failed: expected {$expectedTotalUsers}, got {$analyticsData['daily_active_users']}");
        }

        if ($expectedTotalSessions !== $analyticsData['total_sessions']) {
            throw new \RuntimeException("Session count validation failed: expected {$expectedTotalSessions}, got {$analyticsData['total_sessions']}");
        }

        if ($expectedTotalDuration !== $analyticsData['total_session_duration_seconds']) {
            throw new \RuntimeException("Duration validation failed: expected {$expectedTotalDuration}, got {$analyticsData['total_session_duration_seconds']}");
        }

        // 验证留存率范围 [0, 1]
        $retentionFields = [
            'day_1_retention_rate', 'day_7_retention_rate', 'day_30_retention_rate',
            'new_users_day_1_retention_rate', 'new_users_day_7_retention_rate', 'new_users_day_30_retention_rate',
            'active_users_day_1_retention_rate', 'active_users_day_7_retention_rate', 'active_users_day_30_retention_rate'
        ];

        foreach ($retentionFields as $field) {
            if (isset($analyticsData[$field]) && $analyticsData[$field] !== null &&
                ($analyticsData[$field] < 0 || $analyticsData[$field] > 1)) {
                throw new \RuntimeException("Invalid retention rate for {$field}: {$analyticsData[$field]}");
            }
        }

        // 验证留存率递减趋势（通常 1日 >= 7日 >= 30日）
        $this->validateRetentionTrend($analyticsData, 'new_users');
        $this->validateRetentionTrend($analyticsData, 'active_users');
    }
}
